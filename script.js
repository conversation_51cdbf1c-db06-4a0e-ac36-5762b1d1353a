// <PERSON>h sách bài hát mẫu cho từng album
const albums = {
  "Lê Quyên": [
    { title: "Tim Lai Lan Nua", file: "assets/music/TimLaiLanNua-LeQuyen-2454351.mp3" },
    { title: "<PERSON><PERSON> Cu Da Qua", file: "assets/music/TinhCuDaQua-DangKhoiLeQuyen-1052073.mp3" },
	{ title: "<PERSON>h Khong La <PERSON>", file: "assets/music/TinhKhongLaMo-LeQuyen_3m45k.mp3" },
    { title: "Van Biet La Nhung Nho", file: "assets/music/VanBietLaNhungNho-LeQuyen-2454356.mp3" },
	{ title: "Vi <PERSON>", file: "assets/music/Vi Em <PERSON> - <PERSON>.m4a" },
	{ title: "Xin <PERSON>", file: "assets/music/XinHayChoQuen-LeQuyen-2454407.mp3" },
    { title: "Vi anh da mat", file: "assets/music/Vianhdamat-LeQuyenHoNgocHa-137280.mp3" }
  ],
  "Bằng Kiều": [
    { title: "<PERSON><PERSON>", file: "assets/music/<PERSON><PERSON> - <PERSON> Kieu.mp3" },
	{ title: "Phut Cuoi", file: "assets/music/Phut Cuoi - Bang Kieu.mp3" },
	{ title: "Chi Con Lai Tinh Yeu", file: "assets/music/Chi Con Lai Tinh Yeu - Bang Kieu.mp3" },
	{ title: "Co Le", file: "assets/music/Co Le - Bang Kieu.mp3" },
	{ title: "Em La Tat Ca", file: "assets/music/Em La Tat Ca - Bang Kieu.mp3" },
	{ title: "Lo Yeu Em Roi", file: "assets/music/Lo Yeu Em Roi - Bang Kieu.m4a" },
	{ title: "Mot Lan Nao Cho Toi Gap Lai Em", file: "assets/music/Mot Lan Nao Cho Toi Gap Lai Em - Bang Ki.m4a" },
	{ title: "Lai Gan Hon Anh", file: "assets/music/Lai Gan Hon Anh - Bang Kieu.m4a" },
    { title: "Nàng", file: "assets/music/Nang - Bang Kieu.m4a" }
  ],
  "Tuấn Hưng": [
    { title: "Tinh Yeu Nao Phai Tro Choi", file: "assets/music/TinhYeuNaoPhaiTroChoi-TuanHung_wne6.mp3" },
    { title: "Tha Thu Loi Lam", file: "assets/music/Tha Thu Loi Lam - Tuan Hung.mp3" },
	{ title: "That Long Anh Xin Loi", file: "assets/music/That Long Anh Xin Loi - Tuan Hung.mp3" },
    { title: "Dem Dinh Menh", file: "assets/music/DemDinhMenh-TuanHung-2555321.mp3" }	
  ],
  "Đan Trường": [
    { title: "Mai Mai Mot Tinh Yeu", file: "assets/music/MaiMaiMotTinhYeu-DanTruong_bbf.mp3" },
    { title: "Buoc Chan Le Loi", file: "assets/music/BuocChanLeLoi-DanTruong_3tvaj.mp3" },
	{ title: "Di Ve Noi Xa", file: "assets/music/DiVeNoiXa-DanTruong_94v (1).mp3" },
    { title: "Tinh Quay Got", file: "assets/music/TinhQuayGot-DanTruong_45q6j.mp3" }	
  ],
  "Hồ Ngọc Hà": [
    { title: "Mot Lan Cuoi Thoi", file: "assets/music/MotLanCuoiThoi-HoNgocHa_3b33n.mp3" },
    { title: "Gui Nguoi Yeu Cu", file: "assets/music/GuiNguoiYeuCu-HoNgocHa-4536306.mp3" },
	{ title: "Ca Mot Troi Thuong Nho", file: "assets/music/CaMotTroiThuongNho-HoNgocHa-5103255.mp3" },
    { title: "Co Don Giua Cuoc Tinh", file: "assets/music/CoDonGiuaCuocTinhNewVersion-HoNgocHa-3265535.mp3" }
  ]
};

// Hiển thị danh sách bài hát khi click album
document.querySelectorAll(".album-card").forEach(card => {
  card.addEventListener("click", () => {
    const albumName = card.querySelector(".album-title").textContent;
    const songs = albums[albumName];

    if (!songs) return;

    const main = document.getElementById("main-content");
    main.innerHTML = `
      <header class="main-header">
        <h2>${albumName}</h2>
      </header>
      <section class="song-list">
        ${songs.map(song => `
          <div class="song-item" data-src="${song.file}">
            🎵 ${song.title}
          </div>
        `).join("")}
      </section>
    `;

    document.querySelectorAll(".song-item").forEach(item => {
      item.addEventListener("click", () => {
        const src = item.getAttribute("data-src");
        const title = item.textContent.trim();

        const audio = document.getElementById("audio-player");
        const nowPlaying = document.getElementById("now-playing");

        audio.src = src;
        audio.play();
        nowPlaying.textContent = title;
      });
    });
  });
});

// Các nút menu (chưa dùng, để sau)
function showHome() {
  alert("Trang chủ chưa triển khai");
}

function showSearch() {
  alert("Tìm kiếm chưa triển khai");
}

function showAlbums() {
  location.reload(); // load lại danh sách album
}
