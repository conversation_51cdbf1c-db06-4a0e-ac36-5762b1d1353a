// <PERSON><PERSON> sách bài hát mẫu cho từng album
let albums = {
  "Lê Quyên": [
    { title: "Tim Lai Lan Nua", file: "assets/music/TimLaiLanNua-LeQuyen-2454351.mp3" },
    { title: "<PERSON><PERSON>u Da Qua", file: "assets/music/TinhCuDaQua-DangKhoiLeQuyen-1052073.mp3" },
	{ title: "<PERSON>h Khong La Mo", file: "assets/music/TinhKhongLaMo-LeQuyen_3m45k.mp3" },
    { title: "Van Biet La Nhung Nho", file: "assets/music/VanBietLaNhungNho-LeQuyen-2454356.mp3" },
	{ title: "Vi <PERSON>", file: "assets/music/Vi Em <PERSON> - <PERSON>.m4a" },
	{ title: "Xin Hay Cho Quen", file: "assets/music/XinHayChoQuen-LeQuyen-2454407.mp3" },
    { title: "Vi anh da mat", file: "assets/music/Vianhdamat-LeQ<PERSON>enHoNgocHa-137280.mp3" }
  ],
  "Bằng Kiều": [
    { title: "<PERSON><PERSON>", file: "assets/music/<PERSON>h <PERSON> - <PERSON>eu.mp3" },
	{ title: "Phut Cuoi", file: "assets/music/Phut Cuoi - Bang Kieu.mp3" },
	{ title: "Chi Con Lai Tinh Yeu", file: "assets/music/Chi Con Lai Tinh Yeu - Bang Kieu.mp3" },
	{ title: "Co Le", file: "assets/music/Co Le - Bang Kieu.mp3" },
	{ title: "Em La Tat Ca", file: "assets/music/Em La Tat Ca - Bang Kieu.mp3" },
	{ title: "Lo Yeu Em Roi", file: "assets/music/Lo Yeu Em Roi - Bang Kieu.m4a" },
	{ title: "Mot Lan Nao Cho Toi Gap Lai Em", file: "assets/music/Mot Lan Nao Cho Toi Gap Lai Em - Bang Ki.m4a" },
	{ title: "Lai Gan Hon Anh", file: "assets/music/Lai Gan Hon Anh - Bang Kieu.m4a" },
    { title: "Nàng", file: "assets/music/Nang - Bang Kieu.m4a" }
  ],
  "Tuấn Hưng": [
    { title: "Tinh Yeu Nao Phai Tro Choi", file: "assets/music/TinhYeuNaoPhaiTroChoi-TuanHung_wne6.mp3" },
    { title: "Tha Thu Loi Lam", file: "assets/music/Tha Thu Loi Lam - Tuan Hung.mp3" },
	{ title: "That Long Anh Xin Loi", file: "assets/music/That Long Anh Xin Loi - Tuan Hung.mp3" },
    { title: "Dem Dinh Menh", file: "assets/music/DemDinhMenh-TuanHung-2555321.mp3" }	
  ],
  "Đan Trường": [
    { title: "Mai Mai Mot Tinh Yeu", file: "assets/music/MaiMaiMotTinhYeu-DanTruong_bbf.mp3" },
    { title: "Buoc Chan Le Loi", file: "assets/music/BuocChanLeLoi-DanTruong_3tvaj.mp3" },
	{ title: "Di Ve Noi Xa", file: "assets/music/DiVeNoiXa-DanTruong_94v (1).mp3" },
    { title: "Tinh Quay Got", file: "assets/music/TinhQuayGot-DanTruong_45q6j.mp3" }	
  ],
  "Hồ Ngọc Hà": [
    { title: "Mot Lan Cuoi Thoi", file: "assets/music/MotLanCuoiThoi-HoNgocHa_3b33n.mp3" },
    { title: "Gui Nguoi Yeu Cu", file: "assets/music/GuiNguoiYeuCu-HoNgocHa-4536306.mp3" },
	{ title: "Ca Mot Troi Thuong Nho", file: "assets/music/CaMotTroiThuongNho-HoNgocHa-5103255.mp3" },
    { title: "Co Don Giua Cuoc Tinh", file: "assets/music/CoDonGiuaCuocTinhNewVersion-HoNgocHa-3265535.mp3" }
  ]
};

// Biến lưu trữ albums được quét
let scannedAlbums = {};

// Main initialization
document.addEventListener('DOMContentLoaded', function() {
  // Event listeners cho menu
  document.getElementById('menu-home').addEventListener('click', showHome);
  document.getElementById('menu-search').addEventListener('click', showSearch);
  document.getElementById('menu-albums').addEventListener('click', showAlbums);

  // Event listeners cho album cards hiện có
  document.querySelectorAll(".album-card").forEach(card => {
    card.addEventListener("click", () => {
      const albumName = card.querySelector(".album-title").textContent;
      showAlbumSongs(albumName);
    });
  });

  // File Scanner Functionality
  const folderInput = document.getElementById('folder-input');
  const selectFolderBtn = document.getElementById('select-folder-btn');
  const scanStatus = document.getElementById('scan-status');
  const scanProgress = document.getElementById('scan-progress');
  const progressFill = document.getElementById('progress-fill');
  const progressText = document.getElementById('progress-text');

  // Xử lý chọn thư mục
  selectFolderBtn.addEventListener('click', () => {
    folderInput.click();
  });

  // Xử lý khi chọn files
  folderInput.addEventListener('change', async (event) => {
    const files = Array.from(event.target.files);
    const audioFiles = files.filter(file =>
      file.type.startsWith('audio/') ||
      file.name.toLowerCase().match(/\.(mp3|wav|flac|m4a|aac|ogg)$/i)
    );

    if (audioFiles.length === 0) {
      scanStatus.textContent = 'Không tìm thấy file audio nào';
      return;
    }

    await scanAudioFiles(audioFiles);
  });

  // Hàm quét file audio
  async function scanAudioFiles(files) {
    scanStatus.textContent = `Đang quét ${files.length} file...`;
    scanProgress.style.display = 'block';
    selectFolderBtn.disabled = true;

    scannedAlbums = {};

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const progress = ((i + 1) / files.length) * 100;

      progressFill.style.width = `${progress}%`;
      progressText.textContent = `${Math.round(progress)}%`;

      // Phân tích đường dẫn để tạo album
      const pathParts = file.webkitRelativePath.split('/');
      let albumName = 'Unknown Album';

      if (pathParts.length > 1) {
        // Lấy tên thư mục cha làm album name
        albumName = pathParts[pathParts.length - 2];
      }

      // Tạo URL cho file
      const fileURL = URL.createObjectURL(file);

      // Lấy tên bài hát từ tên file
      const songTitle = file.name.replace(/\.[^/.]+$/, ""); // Bỏ extension

      if (!scannedAlbums[albumName]) {
        scannedAlbums[albumName] = [];
      }

      scannedAlbums[albumName].push({
        title: songTitle,
        file: fileURL,
        originalFile: file
      });

      // Delay nhỏ để UI không bị đơ
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // Cập nhật albums chính
    albums = { ...albums, ...scannedAlbums };

    // Hiển thị kết quả
    scanStatus.textContent = `Đã quét xong! Tìm thấy ${Object.keys(scannedAlbums).length} album`;
    selectFolderBtn.disabled = false;

    // Ẩn progress sau 2 giây
    setTimeout(() => {
      scanProgress.style.display = 'none';
    }, 2000);

    // Cập nhật giao diện
    updateAlbumDisplay();
    showScannedAlbums();
  }

  // Hiển thị danh sách albums đã quét trong sidebar
  function showScannedAlbums() {
    const fileScanner = document.getElementById('file-scanner');

    // Xóa danh sách cũ nếu có
    const existingList = fileScanner.querySelector('.scanned-albums');
    if (existingList) {
      existingList.remove();
    }

    if (Object.keys(scannedAlbums).length > 0) {
      const scannedDiv = document.createElement('div');
      scannedDiv.className = 'scanned-albums';
      scannedDiv.innerHTML = `
        <h4>📁 Albums đã quét:</h4>
        ${Object.entries(scannedAlbums).map(([albumName, songs]) => `
          <div class="scanned-album-item" data-album="${albumName}">
            ${albumName}
            <div class="scanned-album-count">${songs.length} bài</div>
          </div>
        `).join('')}
      `;

      fileScanner.appendChild(scannedDiv);

      // Thêm event listener cho các album đã quét
      scannedDiv.querySelectorAll('.scanned-album-item').forEach(item => {
        item.addEventListener('click', () => {
          const albumName = item.getAttribute('data-album');
          showAlbumSongs(albumName);
        });
      });
    }
  }

  // Cập nhật hiển thị album trong main content
  function updateAlbumDisplay() {
    const albumList = document.getElementById('album-list');
    const newAlbums = Object.keys(scannedAlbums);

    newAlbums.forEach(albumName => {
      // Kiểm tra xem album đã có trong giao diện chưa
      const existingCard = Array.from(albumList.querySelectorAll('.album-card'))
        .find(card => card.querySelector('.album-title').textContent === albumName);

      if (!existingCard) {
        const albumCard = document.createElement('div');
        albumCard.className = 'album-card';
        albumCard.innerHTML = `
          <div style="width: 100%; height: 180px; background: linear-gradient(45deg, #1db954, #1ed760); display: flex; align-items: center; justify-content: center; font-size: 48px;">
            🎵
          </div>
          <div class="album-info">
            <span class="album-title">${albumName}</span>
          </div>
        `;

        albumList.appendChild(albumCard);

        // Thêm event listener cho album mới
        albumCard.addEventListener('click', () => {
          showAlbumSongs(albumName);
        });
      }
    });
  }
}); // End of DOMContentLoaded

// Hàm hiển thị bài hát của album
function showAlbumSongs(albumName) {
  const songs = albums[albumName];
  if (!songs) return;

  const main = document.getElementById("main-content");
  main.innerHTML = `
    <header class="main-header">
      <h2>${albumName}</h2>
      <p style="color: #ccc; font-size: 14px;">${songs.length} bài hát</p>
    </header>
    <section class="song-list">
      ${songs.map((song, index) => `
        <div class="song-item" data-src="${song.file}" data-title="${song.title}">
          <span style="color: #666; margin-right: 10px;">${index + 1}</span>
          🎵 ${song.title}
        </div>
      `).join("")}
    </section>
  `;

  // Thêm event listener cho các bài hát
  document.querySelectorAll(".song-item").forEach(item => {
    item.addEventListener("click", () => {
      const src = item.getAttribute("data-src");
      const title = item.getAttribute("data-title");

      const audio = document.getElementById("audio-player");
      const nowPlaying = document.getElementById("now-playing-title");

      audio.src = src;
      audio.play();
      nowPlaying.textContent = title;
    });
  });
}

// Các nút menu
function showHome() {
  const main = document.getElementById("main-content");
  main.innerHTML = `
    <header class="main-header">
      <h2>🏠 Trang chủ</h2>
    </header>
    <div style="padding: 20px; text-align: center;">
      <h3>Chào mừng đến với MyPlayer!</h3>
      <p>Sử dụng chức năng "Quét thư mục" để thêm nhạc của bạn</p>
      <p>Hoặc chọn từ các album có sẵn</p>
    </div>
  `;
}

function showSearch() {
  const main = document.getElementById("main-content");
  main.innerHTML = `
    <header class="main-header">
      <h2>🔍 Tìm kiếm</h2>
    </header>
    <div style="padding: 20px;">
      <input type="text" id="search-input" placeholder="Tìm kiếm bài hát, album..."
             style="width: 100%; padding: 10px; border-radius: 20px; border: none; background: #333; color: white;">
      <div id="search-results" style="margin-top: 20px;"></div>
    </div>
  `;

  // Thêm chức năng tìm kiếm
  const searchInput = document.getElementById('search-input');
  const searchResults = document.getElementById('search-results');

  searchInput.addEventListener('input', (e) => {
    const query = e.target.value.toLowerCase();
    if (query.length < 2) {
      searchResults.innerHTML = '';
      return;
    }

    const results = [];
    Object.entries(albums).forEach(([albumName, songs]) => {
      songs.forEach(song => {
        if (song.title.toLowerCase().includes(query) || albumName.toLowerCase().includes(query)) {
          results.push({ ...song, albumName });
        }
      });
    });

    searchResults.innerHTML = results.length > 0 ? `
      <h3>Kết quả tìm kiếm:</h3>
      ${results.map(result => `
        <div class="song-item" data-src="${result.file}" data-title="${result.title}">
          🎵 ${result.title} - <span style="color: #888;">${result.albumName}</span>
        </div>
      `).join('')}
    ` : '<p>Không tìm thấy kết quả nào</p>';

    // Thêm event listener cho kết quả tìm kiếm
    searchResults.querySelectorAll('.song-item').forEach(item => {
      item.addEventListener('click', () => {
        const src = item.getAttribute('data-src');
        const title = item.getAttribute('data-title');

        const audio = document.getElementById('audio-player');
        const nowPlaying = document.getElementById('now-playing-title');

        audio.src = src;
        audio.play();
        nowPlaying.textContent = title;
      });
    });
  });
}

function showAlbums() {
  location.reload(); // load lại danh sách album
}
