/* FONT */
body {
  margin: 0;
  font-family: 'Roboto', sans-serif;
  background-color: #121212;
  color: #fff;
}

/* GRID */
.app {
  display: grid;
  grid-template-columns: 240px 1fr;
  grid-template-rows: 1fr 90px;
  height: 100vh;
  overflow: hidden;
}

/* SIDEBAR */
.sidebar {
  background-color: #000;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #1db954;
}

.menu ul {
  list-style: none;
  padding: 0;
}

.menu li {
  padding: 12px 0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.menu li:hover {
  color: #1db954;
}

/* MAIN */
.main-content {
  padding: 20px;
  overflow-y: auto;
}

.main-header h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
}

/* ALBUM */
.album-list {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.album-card {
  width: 180px;
  background-color: #181818;
  border-radius: 8px;
  overflow: hidden;
  transition: background-color 0.3s;
  cursor: pointer;
}

.album-card:hover {
  background-color: #282828;
}

.album-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.album-info {
  padding: 10px;
  text-align: center;
}

.album-title {
  font-weight: 500;
  color: #fff;
}

/* FOOTER - MUSIC PLAYER */
.music-player {
  grid-column: span 2;
  background-color: #181818;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-top: 1px solid #333;
}

.now-playing {
  font-size: 14px;
  color: #ccc;
}

#audio-player {
  width: 300px;
}
.song-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.song-item {
  padding: 10px;
  background-color: #1e1e1e;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.song-item:hover {
  background-color: #2a2a2a;
}

/* FILE SCANNER */
.file-scanner {
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.file-scanner h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #1db954;
}

.scanner-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.scan-btn {
  background-color: #1db954;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.scan-btn:hover {
  background-color: #1ed760;
}

.scan-btn:disabled {
  background-color: #666;
  cursor: not-allowed;
}

.scan-status {
  font-size: 12px;
  color: #ccc;
  text-align: center;
}

.scan-progress {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1db954;
  width: 0%;
  transition: width 0.3s ease;
}

#progress-text {
  font-size: 12px;
  color: #ccc;
  text-align: center;
}

/* SCANNED ALBUMS */
.scanned-albums {
  margin-top: 20px;
}

.scanned-albums h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #1db954;
}

.scanned-album-item {
  padding: 8px;
  background-color: #2a2a2a;
  border-radius: 4px;
  margin-bottom: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.scanned-album-item:hover {
  background-color: #3a3a3a;
}

.scanned-album-count {
  color: #888;
  font-size: 11px;
}
